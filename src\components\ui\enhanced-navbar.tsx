"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Menu, X, PhoneCall } from "lucide-react";
import { Button } from "@/components/ui/button";
import { HoveredLink, ProductItem } from "@/components/ui/navbar-menu";
import { cn } from "@/lib/utils";

// FunnelVision logo component
const Logo = () => (
  <a href="/" className="flex items-center hover:opacity-80 transition-opacity">
    <img
      src="/images/logos/funnelvision-logo.svg"
      alt="FunnelVision"
      className="h-8 w-auto"
    />
  </a>
);

interface NavItemProps {
  title: string;
  children?: React.ReactNode;
  isMobile?: boolean;
  onClose?: () => void;
}

const NavItem: React.FC<NavItemProps> = ({ title, children, isMobile, onClose }) => {
  const [isOpen, setIsOpen] = useState(false);

  if (isMobile) {
    return (
      <div className="w-full">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full text-left py-3 px-4 text-foreground hover:bg-muted/50 rounded-md transition-colors"
        >
          {title}
        </button>
        <AnimatePresence>
          {isOpen && children && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden bg-muted/30 rounded-md ml-4 mt-2"
            >
              <div className="p-4">
                {children}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <button className="font-redhat font-medium text-base text-foreground hover:text-primary transition-colors px-4 py-2">
        {title}
      </button>
      <AnimatePresence>
        {isOpen && children && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50"
          >
            <div className="bg-background border border-border rounded-lg shadow-lg p-4 min-w-[200px]">
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export function EnhancedNavbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // Show/hide navbar based on scroll direction
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else {
        setIsVisible(true);
      }
      
      // Add background when scrolled
      setIsScrolled(currentScrollY > 50);
      setLastScrollY(currentScrollY);
    };

    window.addEventListener("scroll", handleScroll, { passive: true });
    return () => window.removeEventListener("scroll", handleScroll);
  }, [lastScrollY]);

  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  return (
    <motion.header
      initial={{ y: 0 }}
      animate={{ y: isVisible ? 0 : -100 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
        isScrolled 
          ? "bg-background/80 backdrop-blur-md border-b border-border/50" 
          : "bg-transparent"
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2">
            <NavItem title="Services">
              <div className="flex flex-col space-y-2">
                <HoveredLink to="/services/ai-discovery">AI Discovery & Search</HoveredLink>
                <HoveredLink to="/services/paid-search">Paid Search Orchestration</HoveredLink>
                <HoveredLink to="/services/cro-ux">CRO & UX Loop</HoveredLink>
                <HoveredLink to="/services/web-development">Web Development</HoveredLink>
                <HoveredLink to="/services/seo-content">SEO & Content Engineering</HoveredLink>
                <HoveredLink to="/services/data-analytics">Data & Analytics</HoveredLink>
              </div>
            </NavItem>
            
            <HoveredLink to="/blog" className="font-redhat font-medium text-sm md:text-base px-4 py-2 text-foreground hover:text-primary transition-colors">
              Blog
            </HoveredLink>
          </nav>

          {/* Desktop CTA Button */}
          <div className="hidden md:block">
            <Button className="font-redhat font-medium text-sm md:text-base tracking-tight bg-svibi hover:bg-svibi/90 text-white" onClick={() => window.location.href = '/book-a-call'}>
              Book Profit-Action Plan
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden p-2 text-foreground hover:text-primary transition-colors"
          >
            {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden overflow-hidden bg-background/95 backdrop-blur-md border-t border-border/50"
            >
              <div className="py-4 space-y-2">
                <NavItem title="Services" isMobile onClose={closeMobileMenu}>
                  <div className="flex flex-col space-y-2">
                    <HoveredLink to="/services/ai-discovery">AI Discovery & Search</HoveredLink>
                    <HoveredLink to="/services/paid-search">Paid Search Orchestration</HoveredLink>
                    <HoveredLink to="/services/cro-ux">CRO & UX Loop</HoveredLink>
                    <HoveredLink to="/services/web-development">Web Development</HoveredLink>
                    <HoveredLink to="/services/seo-content">SEO & Content Engineering</HoveredLink>
                    <HoveredLink to="/services/data-analytics">Data & Analytics</HoveredLink>
                  </div>
                </NavItem>
                
                <div className="px-4 py-2">
                  <HoveredLink
                    to="/blog"
                    className="font-redhat font-medium text-base block text-foreground hover:text-primary transition-colors"
                    onClick={closeMobileMenu}
                  >
                    Blog
                  </HoveredLink>
                </div>
                
                <div className="px-4 pt-4">
                  <Button className="font-redhat font-medium text-sm md:text-base tracking-tight w-full bg-svibi hover:bg-svibi/90 text-white" onClick={() => { closeMobileMenu(); window.location.href = '/book-a-call'; }}>
                    Book Profit-Action Plan
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
}
