import { InteractiveTabs } from "@/components/ui/interactive-tabs";

const tabsData = [
  {
    id: "discover",
    label: "Discover",
    title: "Metomic's data security platform automatically detects and secures the data that matters to you",
    description: "Cut through the noise to prioritise your most high-risk assets",
    benefits: [
      "Benefit/Outcome 1",
      "Benefit/Outcome 2", 
      "Benefit/Outcome 3"
    ],
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center",
    ctaText: "Learn More",
    ctaAction: () => console.log("Discover CTA clicked")
  },
  {
    id: "demand",
    label: "Demand",
    title: "Turn customer intent into profitable demand generation",
    description: "Strategic campaigns that capture high-intent prospects at the perfect moment",
    benefits: [
      "Advanced targeting algorithms",
      "Real-time bid optimization",
      "Cross-platform attribution"
    ],
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
    ctaText: "Get Started",
    ctaAction: () => console.log("Demand CTA clicked")
  },
  {
    id: "authority",
    label: "Authority",
    title: "Build market authority through strategic content and positioning",
    description: "Establish your brand as the go-to solution in your industry",
    benefits: [
      "Thought leadership content",
      "Industry recognition",
      "Expert positioning"
    ],
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center",
    ctaText: "Build Authority",
    ctaAction: () => console.log("Authority CTA clicked")
  },
  {
    id: "convert",
    label: "Convert",
    title: "Optimize every touchpoint to maximize conversion rates",
    description: "Data-driven optimization that turns visitors into customers",
    benefits: [
      "A/B testing framework",
      "Conversion funnel analysis",
      "User experience optimization"
    ],
    image: "https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop&crop=center",
    ctaText: "Optimize Now",
    ctaAction: () => console.log("Convert CTA clicked")
  }
];

function InteractiveTabsDemo() {
  return (
    <div className="w-full">
      <InteractiveTabs tabs={tabsData} />
    </div>
  );
}

export { InteractiveTabsDemo };
