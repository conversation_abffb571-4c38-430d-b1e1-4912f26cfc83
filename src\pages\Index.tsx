import { <PERSON> } from "@/components/ui/animated-hero";
import { EnhancedNavbar } from "@/components/ui/enhanced-navbar";
import { SEOHead, organizationSchema } from "@/components/ui/seo-head";
import { ServicesTabsDemo } from "@/components/ui/services-tabs-demo";
import { GrowthEngineSection } from "@/components/ui/growth-engine-section";
import { ComparisonSection } from "@/components/ui/comparison-section";
import { InteractiveTabsDemo } from "@/components/ui/interactive-tabs-demo";
import { CarouselDemo } from "@/components/ui/carousel-demo";
import { TestimonialsDemo } from "@/components/ui/testimonials-demo";
import { CTADemo } from "@/components/ui/call-to-action-demo";
import { FAQDemo } from "@/components/ui/faq-demo";
import { BlogDemo } from "@/components/ui/blog-demo";
import { ConversionClinicSection } from "@/components/ui/conversion-clinic-videos";
import { ClicksCommerceSection } from "@/components/ui/clicks-commerce-videos";
import { TextGenerateEffectDemo } from "@/components/ui/text-generate-effect-demo";
import { Footer2Demo } from "@/components/ui/footer2-demo";
import { Button } from "@/components/ui/button";
import { MoveRight } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-lungu">
      <SEOHead
        title="Search & AI-Marketing | FunnelVision Agency"
        description="Search & AI-marketing that turns customer intent into margin—at scale. Expert paid search, AI discovery, CRO, and web development services."
        jsonLd={organizationSchema}
      />
      <EnhancedNavbar />
      <div className="pt-16">
        <Hero />
      </div>

      <ServicesTabsDemo />
      <GrowthEngineSection />
      <InteractiveTabsDemo />
      <ComparisonSection />

      <section className="py-20 px-4">
        <div className="sm:max-w-none 2xl:max-w-[1728px] mx-auto">
          {/* Bango container for case studies */}
          <div className="bg-bango rounded-3xl p-6 md:p-8 lg:p-12 mx-auto">
            <div className="text-center mb-16">
              <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl text-white mb-4">
                Our Work <span className="text-gango">Speaks For Itself</span>
              </h2>
            </div>
            <CarouselDemo />
          </div>
        </div>
      </section>
{/* <TestimonialsDemo /> */}
      <CTADemo />
      <FAQDemo />
      <section className="py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl text-libi mb-4">Latest <span className="text-gango">Insights and Updates</span></h2>
          </div>

          {/* Blog Section */}
          <div className="mb-14">
            <BlogDemo />
          </div>

          {/* Conversion Clinic Section */}
          <div className="mb-14">
            <ConversionClinicSection />
          </div>

          {/* Clicks and Commerce Section */}
          <ClicksCommerceSection />
        </div>
      </section>
      <Footer2Demo />
    </div>
  );
};

export default Index;
