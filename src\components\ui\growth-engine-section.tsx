import React from 'react';

function GrowthEngineSection() {
  return (
    <section className="py-20 lg:py-32 bg-lungu">
      <div className="container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl text-libi mb-6">
            One growth engine. <span className="text-gango">Multiple entry points.</span>
          </h2>
          <p className="font-redhat text-base md:text-lg text-libi/70 leading-relaxed max-w-2xl mx-auto">
            Whether you need visibility in AI search, stronger paid performance, or a smoother 
            user journey, we'll meet you where the highest-leverage fix is. Every service stands 
            alone. Together, <span className="font-medium text-libi">they scale even harder.</span>
          </p>
        </div>
      </div>
    </section>
  );
}

export { GrowthEngineSection };
