import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { Search, BarChart3, Zap, Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

function Hero() {

  return (
    <div className="w-full bg-lungu">
      <div className="container mx-auto">
        <div className="flex gap-8 py-20 lg:py-40 items-center justify-center flex-col">
          {/* G2 Rating Badge */}
          <div className="flex items-center gap-2">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-4 h-4 fill-yellow-500 text-yellow-500" />
              ))}
            </div>
            <span className="font-redhat text-sm text-libi/70">G2 Verified 5-Star Rating</span>
          </div>

          <div className="flex gap-6 flex-col items-center">
            <h1 className="font-redhat font-medium leading-tight tracking-tight text-[clamp(2.5rem,8vw,4rem)] md:text-[80px] lg:text-[96px] xl:text-[108px] max-w-4xl text-center">
              <span className="text-libi">Win </span>
              <span className="text-gango">Search.</span>
              <br />
              <span className="text-libi">Win </span>
              <span className="text-gango">Conversion.</span>
            </h1>

            <div className="flex flex-col gap-2 text-center max-w-2xl">
              <p className="font-redhat text-lg md:text-xl leading-relaxed text-libi">
                Own results across AI answers, Paid Search, and SEO.
              </p>
              <p className="font-redhat text-lg md:text-xl leading-relaxed text-libi">
                Turn visibility into profit with CRO and UX that scale.
              </p>
            </div>
          </div>
          <div className="flex flex-col items-center gap-3">
            <Button
              size="lg"
              className="font-redhat font-medium text-base md:text-lg tracking-tight bg-svibi hover:bg-svibi/90 text-white h-[54px] px-8 rounded-full"
              onClick={() => window.location.href = '/book-a-call'}
            >
              Get your Action Plan
            </Button>
            <p className="font-redhat text-sm text-libi/60">
              Free 90-day roadmap
            </p>
          </div>


          {/* Feature Cards */}
          <div className="flex flex-col md:flex-row gap-4 mt-8 max-w-5xl w-full px-4">
            <div className="bg-white rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 shadow-sm min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <Search className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Win visibility in AI answers</span>
            </div>

            <div className="bg-white rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 shadow-sm min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <BarChart3 className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Scale with search that compounds</span>
            </div>

            <div className="bg-white rounded-2xl p-4 md:p-6 flex items-center gap-3 md:gap-4 flex-1 shadow-sm min-w-0">
              <div className="w-8 h-8 md:w-10 md:h-10 bg-gango/10 rounded-full flex items-center justify-center flex-shrink-0">
                <Zap className="w-4 h-4 md:w-5 md:h-5 text-gango" />
              </div>
              <span className="font-redhat font-medium text-sm md:text-base text-libi leading-tight whitespace-nowrap">Convert demand with CRO & UX</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { Hero };