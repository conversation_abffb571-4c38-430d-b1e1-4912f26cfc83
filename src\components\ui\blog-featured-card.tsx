import { BlogArticle } from '@/types/blog';
import { BLOG_CATEGORIES } from '@/types/blog';
import { formatReadingTime } from '@/lib/reading-time';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BlogFeaturedCardProps {
  article: BlogArticle;
}

export function BlogFeaturedCard({ article }: BlogFeaturedCardProps) {
  const categoryInfo = BLOG_CATEGORIES.find(cat => cat.id === article.category);
  const publishDate = new Date(article.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const handleCardClick = () => {
    window.location.href = `/blog/${article.slug}`;
  };

  const handleReadMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.location.href = `/blog/${article.slug}`;
  };

  return (
    <div
      className="group relative cursor-pointer mb-16"
      onClick={handleCardClick}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Content */}
        <div className="space-y-6">
          {/* Date and Category */}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{publishDate}</span>
            <span className="bg-primary/10 text-primary px-3 py-1 rounded-card text-sm font-medium">
              {categoryInfo?.name}
            </span>
          </div>

          {/* Title */}
          <h1 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl group-hover:text-primary transition-colors">
            {article.title}
          </h1>

          {/* Description */}
          <p className="font-redhat text-base md:text-lg text-muted-foreground leading-relaxed">
            {article.description}
          </p>

          {/* Reading Time and CTA */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">
              {formatReadingTime(article.readingTime)}
            </span>
            <Button
              className="font-redhat font-medium text-base md:text-lg tracking-tight gap-2"
              onClick={handleReadMoreClick}
            >
              Read full article
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Featured Image */}
        <div className="relative overflow-hidden rounded-card aspect-video bg-muted">
          <img
            src={article.thumbnail}
            alt={article.title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
          />
          
          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
      </div>
    </div>
  );
}
