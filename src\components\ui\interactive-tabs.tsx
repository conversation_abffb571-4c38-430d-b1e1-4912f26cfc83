"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'

interface TabData {
  id: string
  label: string
  title: string
  description: string
  benefits: string[]
  image: string
  ctaText: string
  ctaAction: () => void
}

interface InteractiveTabsProps {
  tabs: TabData[]
}

export function InteractiveTabs({ tabs }: InteractiveTabsProps) {
  const [activeTab, setActiveTab] = useState(-1) // Start with no active tab
  const [isScrollingUp, setIsScrollingUp] = useState(false)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [hasEnteredSection, setHasEnteredSection] = useState(false)
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([])
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = sectionRefs.current.findIndex(ref => ref === entry.target)
            if (index !== -1) {
              setHasEnteredSection(true)
              setActiveTab(index)
            }
          }
        })
      },
      {
        threshold: 0.3,
        rootMargin: '-10% 0px -10% 0px'
      }
    )

    // Also observe the container to know when we enter the section
    const containerObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting && entry.boundingClientRect.top > 0) {
            // We've scrolled above the section
            setHasEnteredSection(false)
            setActiveTab(-1)
          }
        })
      },
      {
        threshold: 0,
        rootMargin: '0px 0px -90% 0px'
      }
    )

    sectionRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref)
    })

    if (containerRef.current) {
      containerObserver.observe(containerRef.current)
    }

    return () => {
      observer.disconnect()
      containerObserver.disconnect()
    }
  }, [])

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      setIsScrollingUp(currentScrollY < lastScrollY)
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  const scrollToSection = (index: number) => {
    setActiveTab(index)
    const section = sectionRefs.current[index]
    if (section) {
      const navHeight = isScrollingUp ? 96 : 64 // Account for nav height
      const offsetTop = section.offsetTop - navHeight
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth'
      })
    }
  }

  return (
    <div ref={containerRef} className="relative w-full">
      {/* Sticky Tab Navigation */}
      <div
        className={`sticky z-40 bg-lungu/95 backdrop-blur-sm border-b border-gray-200 transition-all duration-300 ${
          isScrollingUp ? 'top-16' : 'top-0'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 md:px-6">
          <div className="flex overflow-x-auto scrollbar-hide -mx-4 md:mx-0 px-4 md:px-0 justify-center">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => scrollToSection(index)}
                className={`font-redhat font-medium text-sm md:text-base px-4 md:px-6 py-4 border-b-2 transition-all duration-200 whitespace-nowrap flex-shrink-0 min-w-0 ${
                  activeTab === index
                    ? 'border-svibi text-svibi bg-sulu/10'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Overlapping Sections */}
      <div className="relative">
        {tabs.map((tab, index) => (
          <div
            key={tab.id}
            ref={(el) => (sectionRefs.current[index] = el)}
            className={`sticky min-h-screen flex items-center py-8 bg-lungu transition-all duration-300 ${
              isScrollingUp ? 'top-24' : 'top-16'
            }`}
            style={{
              zIndex: index + 1,
            }}
          >
            <div className="w-full max-w-7xl mx-auto px-6">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                {/* Left Content */}
                <div className="space-y-6 max-w-lg">
                  <h2 className="font-redhat font-medium text-3xl lg:text-4xl text-libi leading-tight">
                    {tab.title}
                  </h2>
                  <p className="font-redhat text-lg text-libi/70 leading-relaxed">
                    {tab.description}
                  </p>

                  {/* Benefits List */}
                  <div className="space-y-3">
                    {tab.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-start space-x-3">
                        <div className="w-1.5 h-1.5 bg-bango rounded-full mt-2.5 flex-shrink-0" />
                        <span className="font-redhat text-base text-libi">{benefit}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    onClick={tab.ctaAction}
                    className="font-redhat font-medium text-base md:text-lg tracking-tight bg-svibi hover:bg-svibi/90 text-white h-[54px]"
                    size="lg"
                  >
                    {tab.ctaText}
                  </Button>
                </div>

                {/* Right Content - Large Visual Card */}
                <div className="bg-sulu rounded-2xl p-8 lg:p-12">
                  <div className="aspect-[4/3] bg-white rounded-xl shadow-xl overflow-hidden border border-gray-200">
                    <img
                      src={tab.image}
                      alt={tab.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
