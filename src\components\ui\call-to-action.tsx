import { MoveRight, PhoneCall } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

function CTA() {
  return (
    <div className="w-full py-20 lg:py-40">
      <div className="container mx-auto">
        <div className="bg-svibi rounded-3xl p-8 md:p-12 text-center">
          <div className="flex flex-col gap-2">
            <h3 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl text-white mb-8">
              Ready to Turn Intent <span className="text-gango">Into Revenue?</span>
            </h3>
            <p className="font-redhat text-base md:text-lg text-white mb-8">
              No hard selling • No commitment
            </p>
          </div>
          <div className="flex flex-row gap-4 justify-center">
            <Button className="font-redhat font-medium text-base md:text-lg tracking-tight gap-4 bg-gango hover:bg-gango/90 text-white h-[54px]" onClick={() => window.location.href = '/book-a-call'}>
              Book Your Free Audit <PhoneCall className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export { CTA };