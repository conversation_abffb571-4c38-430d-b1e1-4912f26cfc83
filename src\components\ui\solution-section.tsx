import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface TabContent {
  id: string;
  title: string;
  headline: string;
  benefits: string[];
  ctaText: string;
  ctaHref?: string;
  imagePlaceholder: string;
}

const tabsData: TabContent[] = [
  {
    id: "discovery",
    title: "Discovery",
    headline: "Get discovered in ChatGPT, Perplexity, and Google's AI Overviews.",
    benefits: [
      "Win buyer attention before competitors show up",
      "Get high-intent traffic without high ad costs",
      "Future-proof your funnel against changing search"
    ],
    ctaText: "Be first in AI answers",
    ctaHref: "/services/ai-discovery",
    imagePlaceholder: "AI Discovery Dashboard"
  },
  {
    id: "demand",
    title: "Demand",
    headline: "Drive qualified traffic through Google & YouTube Ads.",
    benefits: [
      "Acquire customers with high-intent paid traffic",
      "Improve CAC with compounding campaign structure",
      "Scale spend without scaling wasted budget"
    ],
    ctaText: "Drive qualified demand",
    ctaHref: "/services/paid-search",
    imagePlaceholder: "Paid Search Dashboard"
  },
  {
    id: "authority",
    title: "Authority",
    headline: "Build lasting visibility with SEO that compounds.",
    benefits: [
      "Grow trust and traffic that compounds over time",
      "Win visibility in organic and AI-driven results",
      "Strengthen relevance across search and surfaces"
    ],
    ctaText: "Build lasting authority",
    ctaHref: "/services/seo-content",
    imagePlaceholder: "SEO Analytics Dashboard"
  },
  {
    id: "convert",
    title: "Convert",
    headline: "Optimize your funnel to convert more visitors.",
    benefits: [
      "Remove hidden friction killing conversions",
      "Improve flow across mobile, PDPs, and forms",
      "Get more revenue from your existing traffic"
    ],
    ctaText: "Optimize conversions",
    ctaHref: "/services/cro-ux",
    imagePlaceholder: "Conversion Optimization Dashboard"
  }
];

// Desktop Tab Content Component
const DesktopTabContent = ({ tab }: { tab: TabContent }) => (
  <div className="space-y-8 animate-in fade-in-50 slide-in-from-left-5 duration-500">
    <div className="text-4xl font-bold text-black leading-tight">
      {tab.headline}
    </div>

    <ul className="space-y-4 text-base font-medium text-slate-800">
      {tab.benefits.map((benefit, index) => (
        <li
          key={index}
          className="flex items-start gap-2 animate-in fade-in-50 slide-in-from-left-3 duration-300"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <span className="text-svibi font-bold">→</span> {benefit}
        </li>
      ))}
    </ul>

    <Button
      className="bg-svibi text-white px-6 py-3 rounded-full text-sm font-semibold flex items-center gap-2 hover:bg-svibi/90 hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
      onClick={() => tab.ctaHref && (window.location.href = tab.ctaHref)}
    >
      {tab.ctaText} <ArrowRight className="w-4 h-4" />
    </Button>
  </div>
);

// Desktop Tab Visual Component
const DesktopTabVisual = ({ tab }: { tab: TabContent }) => (
  <div className="bg-sango-light p-8 rounded-2xl shadow-xl transition-all duration-500 ease-in-out hover:shadow-2xl hover:scale-105 animate-in fade-in-50 slide-in-from-right-5 duration-500">
    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-6 text-center min-h-[300px] flex flex-col items-center justify-center space-y-4 border border-white/10">
      {/* Animated Icon/Visual Placeholder */}
      <div className="w-16 h-16 bg-white/30 rounded-full flex items-center justify-center mb-4 animate-pulse hover:animate-spin transition-all duration-300">
        <div className="w-8 h-8 bg-white/50 rounded-full"></div>
      </div>

      {/* Dashboard Preview */}
      <div className="w-full space-y-3">
        <div className="h-3 bg-white/30 rounded-full w-3/4 mx-auto animate-pulse"></div>
        <div className="h-2 bg-white/20 rounded-full w-1/2 mx-auto animate-pulse" style={{ animationDelay: '200ms' }}></div>
        <div className="h-2 bg-white/20 rounded-full w-2/3 mx-auto animate-pulse" style={{ animationDelay: '400ms' }}></div>
      </div>

      {/* Chart/Graph Placeholder */}
      <div className="w-full h-20 bg-white/10 rounded-lg flex items-end justify-center space-x-1 p-2">
        {[40, 60, 80, 45, 70, 55, 85].map((height, index) => (
          <div
            key={index}
            className="bg-white/40 rounded-sm transition-all duration-1000 ease-in-out hover:bg-white/60"
            style={{
              width: '8px',
              height: `${height}%`,
              animation: `pulse 2s ease-in-out infinite`,
              animationDelay: `${index * 200}ms`
            }}
          ></div>
        ))}
      </div>

      <div className="text-white/80 text-sm font-medium mt-4 animate-in fade-in-50 duration-700">
        {tab.imagePlaceholder}
      </div>
    </div>
  </div>
);

// Mobile Tab Card Component
const MobileTabCard = ({
  tab,
  index,
  isActive,
  ref
}: {
  tab: TabContent;
  index: number;
  isActive: boolean;
  ref?: (el: HTMLDivElement | null) => void;
}) => (
  <div
    ref={ref}
    className={`transition-all duration-500 ${isActive ? 'opacity-100 scale-100' : 'opacity-70 scale-95'}`}
  >
    <div className="bg-lungu rounded-2xl p-6 mb-4">
      <div className="flex items-center gap-3 mb-4">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300 ${
          isActive ? 'bg-bango text-white shadow-lg' : 'bg-white text-libi'
        }`}>
          {index + 1}
        </div>
        <h3 className="font-redhat font-bold text-xl text-foreground">{tab.title}</h3>
      </div>
    </div>

    <div className="bg-sango-light rounded-2xl p-6 mb-6 shadow-lg transition-all duration-300 hover:shadow-xl">
      <div className="bg-white/20 backdrop-blur-sm rounded-lg p-4 text-center min-h-[200px] flex flex-col items-center justify-center space-y-3 border border-white/10">
        {/* Mobile Visual Elements */}
        <div className="w-12 h-12 bg-white/30 rounded-full flex items-center justify-center animate-pulse">
          <div className="w-6 h-6 bg-white/50 rounded-full"></div>
        </div>

        <div className="w-full space-y-2">
          <div className="h-2 bg-white/30 rounded-full w-2/3 mx-auto"></div>
          <div className="h-2 bg-white/20 rounded-full w-1/2 mx-auto"></div>
        </div>

        <div className="w-full h-12 bg-white/10 rounded flex items-end justify-center space-x-1 p-1">
          {[30, 50, 70, 40, 60].map((height, index) => (
            <div
              key={index}
              className="bg-white/40 rounded-sm"
              style={{
                width: '6px',
                height: `${height}%`
              }}
            ></div>
          ))}
        </div>

        <div className="text-white/80 text-sm font-medium">
          {tab.imagePlaceholder}
        </div>
      </div>
    </div>

    <div className="space-y-4">
      <h4 className="text-2xl font-bold text-black leading-tight">
        {tab.headline}
      </h4>

      <ul className="space-y-3 text-base font-medium text-slate-800">
        {tab.benefits.map((benefit, benefitIndex) => (
          <li key={benefitIndex} className="flex items-start gap-2">
            <span className="text-svibi">→</span> {benefit}
          </li>
        ))}
      </ul>

      <Button
        className="bg-svibi text-white px-6 py-3 rounded-full text-sm font-semibold flex items-center gap-2 hover:bg-svibi/90 transition w-full justify-center"
        onClick={() => tab.ctaHref && (window.location.href = tab.ctaHref)}
      >
        {tab.ctaText} <ArrowRight className="w-4 h-4" />
      </Button>
    </div>
  </div>
);

export const SolutionSection = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const isMobile = useIsMobile();
  const sectionRef = useRef<HTMLElement>(null);
  const tabRefs = useRef<(HTMLDivElement | null)[]>([]);
  const autoAdvanceRef = useRef<NodeJS.Timeout | null>(null);
  const touchStartRef = useRef<{ x: number; y: number } | null>(null);
  const mobileContainerRef = useRef<HTMLDivElement>(null);

  // Auto-advance tabs every 5 seconds (pause when scrolling)
  useEffect(() => {
    if (!isScrolling) {
      autoAdvanceRef.current = setInterval(() => {
        setActiveTab((prev) => (prev + 1) % tabsData.length);
      }, 5000);
    } else {
      if (autoAdvanceRef.current) {
        clearInterval(autoAdvanceRef.current);
        autoAdvanceRef.current = null;
      }
    }

    return () => {
      if (autoAdvanceRef.current) {
        clearInterval(autoAdvanceRef.current);
      }
    };
  }, [isScrolling]);

  // Scroll-aware progress bar for mobile
  useEffect(() => {
    if (!isMobile || !sectionRef.current) return;

    const handleScroll = () => {
      setIsScrolling(true);

      // Clear existing timeout
      if (autoAdvanceRef.current) {
        clearTimeout(autoAdvanceRef.current);
      }

      // Reset scrolling state after 1 second of no scrolling
      autoAdvanceRef.current = setTimeout(() => {
        setIsScrolling(false);
      }, 1000);

      const section = sectionRef.current;
      if (!section) return;

      const sectionRect = section.getBoundingClientRect();
      const sectionHeight = section.offsetHeight;
      const viewportHeight = window.innerHeight;

      // Calculate how much of the section is visible
      const visibleTop = Math.max(0, -sectionRect.top);
      const visibleBottom = Math.min(sectionHeight, viewportHeight - sectionRect.top);
      const visibleHeight = Math.max(0, visibleBottom - visibleTop);
      const scrollProgress = visibleHeight / Math.min(sectionHeight, viewportHeight);

      // Update progress based on scroll position
      const newProgress = Math.min(100, scrollProgress * 100);
      setProgress(newProgress);

      // Update active tab based on scroll position
      const tabIndex = Math.floor((scrollProgress * tabsData.length));
      const clampedIndex = Math.min(Math.max(0, tabIndex), tabsData.length - 1);
      setActiveTab(clampedIndex);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial call

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isMobile]);

  // Update progress based on active tab (for desktop)
  useEffect(() => {
    if (!isMobile) {
      const newProgress = ((activeTab + 1) / tabsData.length) * 100;
      setProgress(newProgress);
    }
  }, [activeTab, isMobile]);

  // Handle tab click
  const handleTabClick = (index: number) => {
    if (index === activeTab) return;

    setIsTransitioning(true);
    setActiveTab(index);
    setIsScrolling(true);

    // Reset states after transition
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);

    setTimeout(() => {
      setIsScrolling(false);
    }, 2000);
  };

  // Touch event handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isMobile) return;

    const touch = e.touches[0];
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY
    };
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!isMobile || !touchStartRef.current) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartRef.current.x;
    const deltaY = touch.clientY - touchStartRef.current.y;

    // Only handle horizontal swipes (ignore vertical scrolling)
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0 && activeTab > 0) {
        // Swipe right - previous tab
        setActiveTab(activeTab - 1);
      } else if (deltaX < 0 && activeTab < tabsData.length - 1) {
        // Swipe left - next tab
        setActiveTab(activeTab + 1);
      }

      // Pause auto-advance temporarily
      setIsScrolling(true);
      setTimeout(() => {
        setIsScrolling(false);
      }, 3000);
    }

    touchStartRef.current = null;
  };

  return (
    <section id="solutions" ref={sectionRef} className="relative py-16 md:py-24 bg-white">
      {/* Sticky Progress Bar */}
      <div className="sticky top-0 z-20 h-2 bg-gango shadow-sm">
        <div
          className="h-full bg-svibi transition-all duration-700 ease-out shadow-lg"
          style={{
            width: `${progress}%`,
            boxShadow: '0 0 10px rgba(28, 33, 32, 0.3)'
          }}
        />
      </div>

      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16 max-w-4xl mx-auto">
          <h2 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl mb-6">
            <span className="text-svibi">Funnels That Drive</span>{" "}
            <span className="text-gango">Profit On A Loop</span>
          </h2>
          <p className="font-redhat text-base md:text-lg text-libi max-w-2xl mx-auto">
            Strategic solutions that turn customer intent into profitable conversions at scale.
            Each stage builds on the last to create sustainable growth loops.
          </p>
        </div>

        {/* Desktop Tab Navigation */}
        {!isMobile && (
          <div className="flex justify-center mb-12">
            <div className="flex bg-lungu rounded-full p-2 gap-2">
              {tabsData.map((tab, index) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabClick(index)}
                  className={`px-6 py-3 rounded-full font-redhat font-medium text-sm md:text-base transition-all duration-300 ${
                    activeTab === index
                      ? 'bg-bango text-white shadow-lg'
                      : 'text-libi hover:bg-white hover:shadow-sm'
                  }`}
                >
                  {index + 1}. {tab.title}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Tab Content */}
        <div className="max-w-6xl mx-auto">
          {isMobile ? (
            // Mobile: Swipeable Cards
            <div className="relative">
              {/* Swipe Indicators */}
              <div className="flex justify-center mb-6 gap-2">
                {tabsData.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handleTabClick(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      activeTab === index
                        ? 'bg-bango shadow-lg'
                        : 'bg-lungu hover:bg-gango/30'
                    }`}
                  />
                ))}
              </div>

              {/* Swipeable Container */}
              <div
                ref={mobileContainerRef}
                className="overflow-hidden"
                onTouchStart={handleTouchStart}
                onTouchEnd={handleTouchEnd}
              >
                <div
                  className="flex transition-transform duration-300 ease-out"
                  style={{
                    transform: `translateX(-${activeTab * 100}%)`,
                    width: `${tabsData.length * 100}%`
                  }}
                >
                  {tabsData.map((tab, index) => (
                    <div
                      key={tab.id}
                      className="w-full flex-shrink-0 px-4"
                      style={{ width: `${100 / tabsData.length}%` }}
                    >
                      <MobileTabCard
                        tab={tab}
                        index={index}
                        isActive={activeTab === index}
                        ref={(el) => {
                          tabRefs.current[index] = el;
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Swipe Hint */}
              <div className="text-center mt-6">
                <p className="text-sm text-libi/60 font-redhat">
                  Swipe left or right to explore solutions
                </p>
              </div>
            </div>
          ) : (
            // Desktop: Horizontal Layout
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <DesktopTabContent tab={tabsData[activeTab]} />
              <DesktopTabVisual tab={tabsData[activeTab]} />
            </div>
          )}
        </div>
      </div>
    </section>
  );
};
