import { BlogArticle } from '@/types/blog';
import { BLOG_CATEGORIES } from '@/types/blog';
import { formatReadingTime } from '@/lib/reading-time';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface BlogCardProps {
  article: BlogArticle;
  className?: string;
}

export function BlogCard({ article, className }: BlogCardProps) {
  const categoryInfo = BLOG_CATEGORIES.find(cat => cat.id === article.category);
  const publishDate = new Date(article.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const handleCardClick = () => {
    window.location.href = `/blog/${article.slug}`;
  };

  const handleReadMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.location.href = `/blog/${article.slug}`;
  };

  return (
    <div
      className={cn(
        "group relative flex flex-col gap-2 cursor-pointer",
        "transform-gpu transition-all duration-300 hover:-translate-y-1",
        className
      )}
      onClick={handleCardClick}
    >
      {/* Thumbnail */}
      <div className="relative overflow-hidden rounded-lg aspect-video mb-4 bg-muted">
        <img
          src={article.thumbnail}
          alt={article.title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        
        {/* Hover overlay with Read More button */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
          <Button
            size="sm"
            className="pointer-events-auto font-redhat font-medium !bg-svibi hover:!bg-svibi/90 !text-white border-0"
            onClick={handleReadMoreClick}
          >
            Read more
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Date and Category */}
      <div className="flex items-center justify-between font-redhat text-sm !text-libi/60 mb-2">
        <span>{publishDate}</span>
        <span className="font-redhat font-medium !bg-gango/10 !text-gango px-2 py-1 rounded text-xs">
          {categoryInfo?.name}
        </span>
      </div>

      {/* Title */}
      <h3 className="font-redhat font-medium leading-normal tracking-tight text-lg md:text-xl line-clamp-2 !text-libi group-hover:!text-gango transition-colors">
        {article.title}
      </h3>

      {/* Description */}
      <p className="font-redhat text-base leading-relaxed !text-libi/70 line-clamp-3 mb-2">
        {article.description}
      </p>

      {/* Reading Time */}
      <div className="font-redhat text-sm !text-libi/60">
        {formatReadingTime(article.readingTime)}
      </div>
    </div>
  );
}
