import { useState, useEffect } from "react";
import { MoveRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getRecentArticles } from "@/lib/blog-api";
import { BlogCard } from "@/components/ui/blog-card";
import { type BlogArticle } from "@/types/blog";

function BlogDemo() {
  const [recentArticles, setRecentArticles] = useState<BlogArticle[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadArticles = async () => {
      try {
        const articles = await getRecentArticles(4);
        setRecentArticles(articles);
      } catch (error) {
        console.error('Error loading recent articles:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArticles();
  }, []);

  return (
    <div className="w-full bg-lungu py-20">
      <div className="container mx-auto flex flex-col gap-14">
        <div className="flex w-full flex-col sm:flex-row sm:justify-between sm:items-center gap-8">
          <h4 className="font-redhat font-medium leading-snug tracking-tight text-2xl md:text-3xl lg:text-4xl max-w-xl !text-libi">
            Latest <span className="!text-gango">articles</span>
          </h4>
          <Button
            className="font-redhat font-medium text-base md:text-lg tracking-tight gap-4 !bg-svibi hover:!bg-svibi/90 !text-white border-0"
            onClick={() => window.location.href = '/blog'}
          >
            View all articles <MoveRight className="w-4 h-4" />
          </Button>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {loading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="bg-libi/10 rounded-lg aspect-video animate-pulse" />
                <div className="h-4 bg-libi/10 rounded animate-pulse" />
                <div className="h-3 bg-libi/10 rounded w-2/3 animate-pulse" />
              </div>
            ))
          ) : (
            recentArticles.map((article) => (
              <BlogCard key={article.id} article={article} />
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export { BlogDemo };
